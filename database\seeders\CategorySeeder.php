<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class CategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            [
                'name' => 'Work',
                'color' => '#e05d38',
                'icon' => 'briefcase',
            ],
            [
                'name' => 'Personal',
                'color' => '#86a7c8',
                'icon' => 'user',
            ],
            [
                'name' => 'Health',
                'color' => '#5a7ca6',
                'icon' => 'heart',
            ],
            [
                'name' => 'Learning',
                'color' => '#466494',
                'icon' => 'book',
            ],
            [
                'name' => 'Shopping',
                'color' => '#334c82',
                'icon' => 'shopping-cart',
            ],
            [
                'name' => 'Finance',
                'color' => '#eea591',
                'icon' => 'dollar-sign',
            ],
        ];

        // Create default categories for each user
        $users = DB::table('users')->get();

        foreach ($users as $user) {
            foreach ($categories as $category) {
                DB::table('categories')->insert([
                    'user_id' => $user->id,
                    'name' => $category['name'],
                    'color' => $category['color'],
                    'icon' => $category['icon'],
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }
        }
    }
}
