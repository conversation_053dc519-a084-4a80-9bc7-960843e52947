import{_ as o}from"./AuthenticatedLayout-D724Z5eC.js";import{f as r,o as l,a as e,u as d,g as i,w as t,b as a,F as m}from"./app-RdpRZIWg.js";import"./ApplicationLogo-B0wU8AlE.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";const _={__name:"Dashboard",setup(n){return(p,s)=>(l(),r(m,null,[e(d(i),{title:"Dashboard"}),e(o,null,{header:t(()=>s[0]||(s[0]=[a("h2",{class:"text-xl font-semibold leading-tight text-gray-800"}," Dashboard ",-1)])),default:t(()=>[s[1]||(s[1]=a("div",{class:"py-12"},[a("div",{class:"mx-auto max-w-7xl sm:px-6 lg:px-8"},[a("div",{class:"overflow-hidden bg-white shadow-sm sm:rounded-lg"},[a("div",{class:"p-6 text-gray-900"}," You're logged in! ")])])],-1))]),_:1,__:[1]})],64))}};export{_ as default};
