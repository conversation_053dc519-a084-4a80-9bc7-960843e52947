import{_ as i}from"./AuthenticatedLayout-DcChlEgz.js";import m from"./DeleteUserForm-DPAL5zkd.js";import l from"./UpdatePasswordForm-CyOR-OW5.js";import r from"./UpdateProfileInformationForm-CipcCXSd.js";import{f as n,o as d,a as t,u as c,g as p,w as o,b as s,F as u}from"./app-TqV1q3b2.js";import"./ApplicationLogo-Dr08bVvN.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./TextInput-CbTwFCD5.js";import"./PrimaryButton-CwqGPKj5.js";const _={class:"py-12"},f={class:"mx-auto max-w-7xl space-y-6 sm:px-6 lg:px-8"},x={class:"bg-white p-4 shadow sm:rounded-lg sm:p-8"},g={class:"bg-white p-4 shadow sm:rounded-lg sm:p-8"},h={class:"bg-white p-4 shadow sm:rounded-lg sm:p-8"},N={__name:"Edit",props:{mustVerifyEmail:{type:Boolean},status:{type:String}},setup(a){return(w,e)=>(d(),n(u,null,[t(c(p),{title:"Profile"}),t(i,null,{header:o(()=>e[0]||(e[0]=[s("h2",{class:"text-xl font-semibold leading-tight text-gray-800"}," Profile ",-1)])),default:o(()=>[s("div",_,[s("div",f,[s("div",x,[t(r,{"must-verify-email":a.mustVerifyEmail,status:a.status,class:"max-w-xl"},null,8,["must-verify-email","status"])]),s("div",g,[t(l,{class:"max-w-xl"})]),s("div",h,[t(m,{class:"max-w-xl"})])])])]),_:1})],64))}};export{N as default};
