<script setup>
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import { Head } from '@inertiajs/vue3';

defineProps({
    stats: Object,
    recentTodos: Array,
    upcomingTodos: Array,
    categories: Array,
    totalCategories: Number,
});

const formatDate = (date) => {
    if (!date) return '';
    return new Date(date).toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric'
    });
};

const getPriorityColor = (priority) => {
    switch (priority) {
        case 'high': return 'text-red-600 bg-red-50 border-red-200';
        case 'medium': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
        case 'low': return 'text-green-600 bg-green-50 border-green-200';
        default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
};

const getStatusColor = (status) => {
    switch (status) {
        case 'completed': return 'text-green-600 bg-green-50';
        case 'pending': return 'text-blue-600 bg-blue-50';
        case 'archived': return 'text-gray-600 bg-gray-50';
        default: return 'text-gray-600 bg-gray-50';
    }
};
</script>

<template>
    <Head title="Todo Dashboard" />

    <AuthenticatedLayout>
        <template #header>
            <h2 class="text-xl font-semibold leading-tight text-foreground">
                Todo Dashboard
            </h2>
        </template>

        <div class="py-6">
            <div class="mx-auto max-w-7xl sm:px-6 lg:px-8">
                <!-- Statistics Cards -->
                <div class="grid grid-cols-1 gap-6 mb-8 sm:grid-cols-2 lg:grid-cols-5">
                    <div class="bg-card rounded-lg shadow-sm p-6 border border-border">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center">
                                    <svg class="w-5 h-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-muted-foreground">Total Todos</p>
                                <p class="text-2xl font-semibold text-foreground">{{ stats.total }}</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-card rounded-lg shadow-sm p-6 border border-border">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                                    <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-muted-foreground">Pending</p>
                                <p class="text-2xl font-semibold text-foreground">{{ stats.pending }}</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-card rounded-lg shadow-sm p-6 border border-border">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                                    <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-muted-foreground">Completed</p>
                                <p class="text-2xl font-semibold text-foreground">{{ stats.completed }}</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-card rounded-lg shadow-sm p-6 border border-border">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center">
                                    <svg class="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-muted-foreground">Overdue</p>
                                <p class="text-2xl font-semibold text-foreground">{{ stats.overdue }}</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-card rounded-lg shadow-sm p-6 border border-border">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center">
                                    <svg class="w-5 h-5 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-muted-foreground">High Priority</p>
                                <p class="text-2xl font-semibold text-foreground">{{ stats.high_priority }}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Main Content Grid -->
                <div class="grid grid-cols-1 gap-8 lg:grid-cols-3">
                    <!-- Recent Todos -->
                    <div class="lg:col-span-2">
                        <div class="bg-card rounded-lg shadow-sm border border-border">
                            <div class="px-6 py-4 border-b border-border">
                                <h3 class="text-lg font-semibold text-foreground">Recent Todos</h3>
                            </div>
                            <div class="p-6">
                                <div v-if="recentTodos.length === 0" class="text-center py-8">
                                    <svg class="mx-auto h-12 w-12 text-muted-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                                    </svg>
                                    <p class="mt-2 text-sm text-muted-foreground">No todos yet. Create your first todo!</p>
                                </div>
                                <div v-else class="space-y-4">
                                    <div v-for="todo in recentTodos" :key="todo.id" class="flex items-center justify-between p-4 bg-muted/50 rounded-lg border border-border">
                                        <div class="flex-1">
                                            <h4 class="font-medium text-foreground">{{ todo.title }}</h4>
                                            <p v-if="todo.description" class="text-sm text-muted-foreground mt-1">{{ todo.description }}</p>
                                            <div class="flex items-center mt-2 space-x-2">
                                                <span :class="getPriorityColor(todo.priority)" class="px-2 py-1 text-xs font-medium rounded-full border">
                                                    {{ todo.priority }}
                                                </span>
                                                <span v-if="todo.category" class="px-2 py-1 text-xs font-medium rounded-full bg-accent/50 text-accent-foreground border border-accent">
                                                    {{ todo.category.name }}
                                                </span>
                                                <span v-if="todo.due_date" class="text-xs text-muted-foreground">
                                                    Due: {{ formatDate(todo.due_date) }}
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Sidebar -->
                    <div class="space-y-6">
                        <!-- Upcoming Todos -->
                        <div class="bg-card rounded-lg shadow-sm border border-border">
                            <div class="px-6 py-4 border-b border-border">
                                <h3 class="text-lg font-semibold text-foreground">Upcoming</h3>
                            </div>
                            <div class="p-6">
                                <div v-if="upcomingTodos.length === 0" class="text-center py-4">
                                    <p class="text-sm text-muted-foreground">No upcoming todos</p>
                                </div>
                                <div v-else class="space-y-3">
                                    <div v-for="todo in upcomingTodos" :key="todo.id" class="p-3 bg-muted/30 rounded-lg border border-border">
                                        <h4 class="font-medium text-foreground text-sm">{{ todo.title }}</h4>
                                        <p class="text-xs text-muted-foreground mt-1">{{ formatDate(todo.due_date) }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Categories -->
                        <div class="bg-card rounded-lg shadow-sm border border-border">
                            <div class="px-6 py-4 border-b border-border">
                                <h3 class="text-lg font-semibold text-foreground">Categories</h3>
                            </div>
                            <div class="p-6">
                                <div v-if="categories.length === 0" class="text-center py-4">
                                    <p class="text-sm text-muted-foreground">No categories yet</p>
                                </div>
                                <div v-else class="space-y-2">
                                    <div v-for="category in categories" :key="category.id" class="flex items-center justify-between p-2 rounded-lg hover:bg-muted/50">
                                        <div class="flex items-center">
                                            <div class="w-3 h-3 rounded-full mr-3" :style="{ backgroundColor: category.color }"></div>
                                            <span class="text-sm font-medium text-foreground">{{ category.name }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </AuthenticatedLayout>
</template>
