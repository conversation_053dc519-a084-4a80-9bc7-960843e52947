import{_ as f}from"./AuthenticatedLayout-DcChlEgz.js";import{f as o,o as r,a as m,u as b,g as h,w as g,b as e,t as d,F as l,k as a,h as c,n as v,l as p}from"./app-TqV1q3b2.js";import"./ApplicationLogo-Dr08bVvN.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";const y={class:"py-6"},_={class:"mx-auto max-w-7xl sm:px-6 lg:px-8"},k={class:"grid grid-cols-1 gap-6 mb-8 sm:grid-cols-2 lg:grid-cols-5"},w={class:"bg-card rounded-lg shadow-sm p-6 border border-border"},C={class:"flex items-center"},j={class:"ml-4"},T={class:"text-2xl font-semibold text-foreground"},M={class:"bg-card rounded-lg shadow-sm p-6 border border-border"},B={class:"flex items-center"},D={class:"ml-4"},N={class:"text-2xl font-semibold text-foreground"},L={class:"bg-card rounded-lg shadow-sm p-6 border border-border"},V={class:"flex items-center"},z={class:"ml-4"},S={class:"text-2xl font-semibold text-foreground"},A={class:"bg-card rounded-lg shadow-sm p-6 border border-border"},H={class:"flex items-center"},P={class:"ml-4"},F={class:"text-2xl font-semibold text-foreground"},O={class:"bg-card rounded-lg shadow-sm p-6 border border-border"},U={class:"flex items-center"},E={class:"ml-4"},R={class:"text-2xl font-semibold text-foreground"},$={class:"grid grid-cols-1 gap-8 lg:grid-cols-3"},q={class:"lg:col-span-2"},G={class:"bg-card rounded-lg shadow-sm border border-border"},I={class:"p-6"},J={key:0,class:"text-center py-8"},K={key:1,class:"space-y-4"},Q={class:"flex-1"},W={class:"font-medium text-foreground"},X={key:0,class:"text-sm text-muted-foreground mt-1"},Y={class:"flex items-center mt-2 space-x-2"},Z={key:0,class:"px-2 py-1 text-xs font-medium rounded-full bg-accent/50 text-accent-foreground border border-accent"},ee={key:1,class:"text-xs text-muted-foreground"},te={class:"space-y-6"},se={class:"bg-card rounded-lg shadow-sm border border-border"},oe={class:"p-6"},re={key:0,class:"text-center py-4"},de={key:1,class:"space-y-3"},ne={class:"font-medium text-foreground text-sm"},ie={class:"text-xs text-muted-foreground mt-1"},le={class:"bg-card rounded-lg shadow-sm border border-border"},ae={class:"p-6"},ce={key:0,class:"text-center py-4"},ue={key:1,class:"space-y-2"},me={class:"flex items-center"},ge={class:"text-sm font-medium text-foreground"},ve={__name:"Dashboard",props:{stats:Object,recentTodos:Array,upcomingTodos:Array,categories:Array,totalCategories:Number},setup(n){const u=i=>i?new Date(i).toLocaleDateString("en-US",{month:"short",day:"numeric",year:"numeric"}):"",x=i=>{switch(i){case"high":return"text-red-600 bg-red-50 border-red-200";case"medium":return"text-yellow-600 bg-yellow-50 border-yellow-200";case"low":return"text-green-600 bg-green-50 border-green-200";default:return"text-gray-600 bg-gray-50 border-gray-200"}};return(i,t)=>(r(),o(l,null,[m(b(h),{title:"Todo Dashboard"}),m(f,null,{header:g(()=>t[0]||(t[0]=[e("h2",{class:"text-xl font-semibold leading-tight text-foreground"}," Todo Dashboard ",-1)])),default:g(()=>[e("div",y,[e("div",_,[e("div",k,[e("div",w,[e("div",C,[t[2]||(t[2]=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-primary",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"})])])],-1)),e("div",j,[t[1]||(t[1]=e("p",{class:"text-sm font-medium text-muted-foreground"},"Total Todos",-1)),e("p",T,d(n.stats.total),1)])])]),e("div",M,[e("div",B,[t[4]||(t[4]=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})])])],-1)),e("div",D,[t[3]||(t[3]=e("p",{class:"text-sm font-medium text-muted-foreground"},"Pending",-1)),e("p",N,d(n.stats.pending),1)])])]),e("div",L,[e("div",V,[t[6]||(t[6]=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 13l4 4L19 7"})])])],-1)),e("div",z,[t[5]||(t[5]=e("p",{class:"text-sm font-medium text-muted-foreground"},"Completed",-1)),e("p",S,d(n.stats.completed),1)])])]),e("div",A,[e("div",H,[t[8]||(t[8]=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})])])],-1)),e("div",P,[t[7]||(t[7]=e("p",{class:"text-sm font-medium text-muted-foreground"},"Overdue",-1)),e("p",F,d(n.stats.overdue),1)])])]),e("div",O,[e("div",U,[t[10]||(t[10]=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-orange-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 10V3L4 14h7v7l9-11h-7z"})])])],-1)),e("div",E,[t[9]||(t[9]=e("p",{class:"text-sm font-medium text-muted-foreground"},"High Priority",-1)),e("p",R,d(n.stats.high_priority),1)])])])]),e("div",$,[e("div",q,[e("div",G,[t[12]||(t[12]=e("div",{class:"px-6 py-4 border-b border-border"},[e("h3",{class:"text-lg font-semibold text-foreground"},"Recent Todos")],-1)),e("div",I,[n.recentTodos.length===0?(r(),o("div",J,t[11]||(t[11]=[e("svg",{class:"mx-auto h-12 w-12 text-muted-foreground",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"})],-1),e("p",{class:"mt-2 text-sm text-muted-foreground"},"No todos yet. Create your first todo!",-1)]))):(r(),o("div",K,[(r(!0),o(l,null,a(n.recentTodos,s=>(r(),o("div",{key:s.id,class:"flex items-center justify-between p-4 bg-muted/50 rounded-lg border border-border"},[e("div",Q,[e("h4",W,d(s.title),1),s.description?(r(),o("p",X,d(s.description),1)):c("",!0),e("div",Y,[e("span",{class:v([x(s.priority),"px-2 py-1 text-xs font-medium rounded-full border"])},d(s.priority),3),s.category?(r(),o("span",Z,d(s.category.name),1)):c("",!0),s.due_date?(r(),o("span",ee," Due: "+d(u(s.due_date)),1)):c("",!0)])])]))),128))]))])])]),e("div",te,[e("div",se,[t[14]||(t[14]=e("div",{class:"px-6 py-4 border-b border-border"},[e("h3",{class:"text-lg font-semibold text-foreground"},"Upcoming")],-1)),e("div",oe,[n.upcomingTodos.length===0?(r(),o("div",re,t[13]||(t[13]=[e("p",{class:"text-sm text-muted-foreground"},"No upcoming todos",-1)]))):(r(),o("div",de,[(r(!0),o(l,null,a(n.upcomingTodos,s=>(r(),o("div",{key:s.id,class:"p-3 bg-muted/30 rounded-lg border border-border"},[e("h4",ne,d(s.title),1),e("p",ie,d(u(s.due_date)),1)]))),128))]))])]),e("div",le,[t[16]||(t[16]=e("div",{class:"px-6 py-4 border-b border-border"},[e("h3",{class:"text-lg font-semibold text-foreground"},"Categories")],-1)),e("div",ae,[n.categories.length===0?(r(),o("div",ce,t[15]||(t[15]=[e("p",{class:"text-sm text-muted-foreground"},"No categories yet",-1)]))):(r(),o("div",ue,[(r(!0),o(l,null,a(n.categories,s=>(r(),o("div",{key:s.id,class:"flex items-center justify-between p-2 rounded-lg hover:bg-muted/50"},[e("div",me,[e("div",{class:"w-3 h-3 rounded-full mr-3",style:p({backgroundColor:s.color})},null,4),e("span",ge,d(s.name),1)])]))),128))]))])])])])])])]),_:1})],64))}};export{ve as default};
