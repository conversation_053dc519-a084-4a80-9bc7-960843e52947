import{x as n,c as l,o as d,w as t,a as e,b as r,u as a,g as p,d as u,n as f,e as c}from"./app-TqV1q3b2.js";import{_}from"./GuestLayout-BRto05fG.js";import{_ as w,a as b,b as g}from"./TextInput-CbTwFCD5.js";import{P as x}from"./PrimaryButton-CwqGPKj5.js";import"./ApplicationLogo-Dr08bVvN.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";const y={class:"mt-4 flex justify-end"},T={__name:"ConfirmPassword",setup(P){const s=n({password:""}),i=()=>{s.post(route("password.confirm"),{onFinish:()=>s.reset()})};return(V,o)=>(d(),l(_,null,{default:t(()=>[e(a(p),{title:"Confirm Password"}),o[2]||(o[2]=r("div",{class:"mb-4 text-sm text-gray-600"}," This is a secure area of the application. Please confirm your password before continuing. ",-1)),r("form",{onSubmit:u(i,["prevent"])},[r("div",null,[e(w,{for:"password",value:"Password"}),e(b,{id:"password",type:"password",class:"mt-1 block w-full",modelValue:a(s).password,"onUpdate:modelValue":o[0]||(o[0]=m=>a(s).password=m),required:"",autocomplete:"current-password",autofocus:""},null,8,["modelValue"]),e(g,{class:"mt-2",message:a(s).errors.password},null,8,["message"])]),r("div",y,[e(x,{class:f(["ms-4",{"opacity-25":a(s).processing}]),disabled:a(s).processing},{default:t(()=>o[1]||(o[1]=[c(" Confirm ")])),_:1,__:[1]},8,["class","disabled"])])],32)]),_:1,__:[2]}))}};export{T as default};
