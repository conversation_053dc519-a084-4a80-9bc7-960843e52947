import{i as x,j as y,v,f as _,o as n,x as h,c,w as d,a as o,h as p,b as r,u as t,g as V,t as B,d as C,S as $,e as f,n as P}from"./app-RdpRZIWg.js";import{_ as S}from"./GuestLayout-DBfF7oyg.js";import{_ as g,a as w,b}from"./TextInput-D0cQyMmD.js";import{P as q}from"./PrimaryButton-BW6Hj42P.js";import"./ApplicationLogo-B0wU8AlE.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";const N=["value"],U={__name:"Checkbox",props:{checked:{type:[A<PERSON>y,Boolean],required:!0},value:{default:null}},emits:["update:checked"],setup(l,{emit:s}){const m=s,i=l,e=x({get(){return i.checked},set(a){m("update:checked",a)}});return(a,u)=>y((n(),_("input",{type:"checkbox",value:l.value,"onUpdate:modelValue":u[0]||(u[0]=k=>e.value=k),class:"rounded border-gray-300 text-indigo-600 shadow-sm focus:ring-indigo-500"},null,8,N)),[[v,e.value]])}},L={key:0,class:"mb-4 text-sm font-medium text-green-600"},R={class:"mt-4"},j={class:"mt-4 block"},D={class:"flex items-center"},E={class:"mt-4 flex items-center justify-end"},H={__name:"Login",props:{canResetPassword:{type:Boolean},status:{type:String}},setup(l){const s=h({email:"",password:"",remember:!1}),m=()=>{s.post(route("login"),{onFinish:()=>s.reset("password")})};return(i,e)=>(n(),c(S,null,{default:d(()=>[o(t(V),{title:"Log in"}),l.status?(n(),_("div",L,B(l.status),1)):p("",!0),r("form",{onSubmit:C(m,["prevent"])},[r("div",null,[o(g,{for:"email",value:"Email"}),o(w,{id:"email",type:"email",class:"mt-1 block w-full",modelValue:t(s).email,"onUpdate:modelValue":e[0]||(e[0]=a=>t(s).email=a),required:"",autofocus:"",autocomplete:"username"},null,8,["modelValue"]),o(b,{class:"mt-2",message:t(s).errors.email},null,8,["message"])]),r("div",R,[o(g,{for:"password",value:"Password"}),o(w,{id:"password",type:"password",class:"mt-1 block w-full",modelValue:t(s).password,"onUpdate:modelValue":e[1]||(e[1]=a=>t(s).password=a),required:"",autocomplete:"current-password"},null,8,["modelValue"]),o(b,{class:"mt-2",message:t(s).errors.password},null,8,["message"])]),r("div",j,[r("label",D,[o(U,{name:"remember",checked:t(s).remember,"onUpdate:checked":e[2]||(e[2]=a=>t(s).remember=a)},null,8,["checked"]),e[3]||(e[3]=r("span",{class:"ms-2 text-sm text-gray-600"},"Remember me",-1))])]),r("div",E,[l.canResetPassword?(n(),c(t($),{key:0,href:i.route("password.request"),class:"rounded-md text-sm text-gray-600 underline hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"},{default:d(()=>e[4]||(e[4]=[f(" Forgot your password? ")])),_:1,__:[4]},8,["href"])):p("",!0),o(q,{class:P(["ms-4",{"opacity-25":t(s).processing}]),disabled:t(s).processing},{default:d(()=>e[5]||(e[5]=[f(" Log in ")])),_:1,__:[5]},8,["class","disabled"])])],32)]),_:1}))}};export{H as default};
