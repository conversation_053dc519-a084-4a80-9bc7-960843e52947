<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Create test users
        User::factory()->create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
        ]);

        User::factory()->create([
            'name' => 'Demo User',
            'email' => '<EMAIL>',
        ]);

        // Seed categories and todos
        $this->call([
            CategorySeeder::class,
            TodoSeeder::class,
        ]);
    }
}
