<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('reminders', function (Blueprint $table) {
            $table->id();
            $table->foreignId('todo_id')->constrained()->onDelete('cascade');
            $table->timestamp('reminder_time');
            $table->boolean('is_sent')->default(false);
            $table->enum('reminder_type', ['email', 'notification', 'both'])->default('notification');
            $table->timestamps();

            $table->index(['todo_id', 'reminder_time']);
            $table->index(['reminder_time', 'is_sent']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('reminders');
    }
};
