# Todo-Reminder-Idea Laravel 12.x Application

A modern, full-stack Todo-Reminder application built with Laravel 12.x, Vue.js 3, and MySQL, featuring Google Calendar integration and a sleek custom theme.

## Features

### Core Functionality
- **Todo Management**: Create, read, update, delete todos with priority levels and categories
- **Reminder System**: Email notifications, in-app notifications, and recurring reminders
- **Calendar Integration**: Built-in calendar view with Google Calendar sync
- **Dashboard & Analytics**: Overview statistics, productivity charts, and completion rates
- **Authentication**: User registration, login, email verification, and profile management

### Modern UI Features
- **Custom Theme**: Tweakcn-based design with light/dark mode support
- **Responsive Design**: Works seamlessly across desktop, tablet, and mobile
- **Real-time Updates**: Live notifications and todo updates
- **Drag & Drop**: Intuitive todo organization
- **Search & Filtering**: Advanced todo filtering and search capabilities
- **Keyboard Shortcuts**: Power user features for efficiency

## Technology Stack

### Backend
- **Framework**: Laravel 12.x
- **Database**: MySQL (via XAMPP)
- **Authentication**: Laravel Sanctum
- **Queue Management**: Laravel Horizon
- **Email**: Laravel Mail with queue support
- **API**: RESTful API design

### Frontend
- **Framework**: Vue.js 3 with Composition API
- **Styling**: Tailwind CSS v4
- **UI Components**: Custom shadcn/ui components
- **State Management**: Pinia
- **Build Tool**: Vite

### Integrations
- **Google Calendar**: Bidirectional sync for todos and events
- **Email Notifications**: SMTP-based reminder system
- **Real-time**: WebSocket support for live updates

## Installation

### Prerequisites
- PHP 8.2 or higher
- Composer
- Node.js and npm/yarn
- XAMPP (for MySQL)
- Git

### Setup Steps

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd todo-reminder-idea
   ```

2. **Install PHP dependencies**
   ```bash
   composer install
   ```

3. **Install Node.js dependencies**
   ```bash
   npm install
   ```

4. **Environment configuration**
   ```bash
   cp .env.example .env
   php artisan key:generate
   ```

5. **Database setup**
   - Start XAMPP and ensure MySQL is running
   - Create a database named `todo_reminder`
   - Update `.env` file with database credentials
   ```bash
   php artisan migrate --seed
   ```

6. **Build frontend assets**
   ```bash
   npm run build
   ```

7. **Start the development server**
   ```bash
   php artisan serve
   ```

## Development

### Project Structure
```
todo-reminder-idea/
├── app/                    # Laravel application logic
│   ├── Http/Controllers/   # API and web controllers
│   ├── Models/            # Eloquent models
│   ├── Services/          # Business logic services
│   └── Jobs/              # Queue jobs
├── database/              # Database files
│   ├── migrations/        # Database migrations
│   └── seeders/          # Database seeders
├── resources/             # Frontend resources
│   ├── js/               # Vue.js components and logic
│   ├── css/              # Stylesheets and theme
│   └── views/            # Blade templates
├── routes/                # Application routes
├── development-logs/      # Development documentation
└── documentation/         # Project documentation
```

### Development Workflow
1. **Backend Development**: Create models, controllers, and services
2. **Frontend Development**: Build Vue.js components and pages
3. **Integration**: Connect frontend with backend APIs
4. **Testing**: Write and run comprehensive tests
5. **Documentation**: Update logs and documentation

### Custom Theme
The application uses a custom theme based on tweakcn with the following color palette:
- **Primary**: #e05d38 (orange-red)
- **Light Background**: #e8ebed
- **Dark Background**: #1c2433
- **Typography**: Roboto Mono, Source Serif 4, JetBrains Mono

## API Documentation

API documentation will be available at `/api/documentation` once the application is running.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Write tests for new functionality
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Support

For support and questions, please refer to the documentation or create an issue in the repository.
