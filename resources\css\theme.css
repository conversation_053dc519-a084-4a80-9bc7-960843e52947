:root {
    --background: #e8ebed;
    --foreground: #333333;
    --card: #ffffff;
    --card-foreground: #333333;
    --popover: #ffffff;
    --popover-foreground: #333333;
    --primary: #e05d38;
    --primary-foreground: #ffffff;
    --secondary: #f3f4f6;
    --secondary-foreground: #4b5563;
    --muted: #f9fafb;
    --muted-foreground: #6b7280;
    --accent: #d6e4f0;
    --accent-foreground: #1e3a8a;
    --destructive: #ef4444;
    --destructive-foreground: #ffffff;
    --border: #dcdfe2;
    --input: #f4f5f7;
    --ring: #e05d38;
    --chart-1: #86a7c8;
    --chart-2: #eea591;
    --chart-3: #5a7ca6;
    --chart-4: #466494;
    --chart-5: #334c82;
    --sidebar: #dddfe2;
    --sidebar-foreground: #333333;
    --sidebar-primary: #e05d38;
    --sidebar-primary-foreground: #ffffff;
    --sidebar-accent: #d6e4f0;
    --sidebar-accent-foreground: #1e3a8a;
    --sidebar-border: #e5e7eb;
    --sidebar-ring: #e05d38;
    --font-sans: Roboto Mono, monospace;
    --font-serif: Source Serif 4, serif;
    --font-mono: JetBrains Mono, monospace;
    --radius: 0.75rem;
    --shadow-2xs: 0px 1px 3px 0px hsl(0 0% 0% / 0.05);
    --shadow-xs: 0px 1px 3px 0px hsl(0 0% 0% / 0.05);
    --shadow-sm: 0px 1px 3px 0px hsl(0 0% 0% / 0.1), 0px 1px 2px -1px hsl(0 0% 0% / 0.1);
    --shadow: 0px 1px 3px 0px hsl(0 0% 0% / 0.1), 0px 1px 2px -1px hsl(0 0% 0% / 0.1);
    --shadow-md: 0px 1px 3px 0px hsl(0 0% 0% / 0.1), 0px 2px 4px -1px hsl(0 0% 0% / 0.1);
    --shadow-lg: 0px 1px 3px 0px hsl(0 0% 0% / 0.1), 0px 4px 6px -1px hsl(0 0% 0% / 0.1);
    --shadow-xl: 0px 1px 3px 0px hsl(0 0% 0% / 0.1), 0px 8px 10px -1px hsl(0 0% 0% / 0.1);
    --shadow-2xl: 0px 1px 3px 0px hsl(0 0% 0% / 0.25);
}

.dark {
    --background: #1c2433;
    --foreground: #e5e5e5;
    --card: #2a3040;
    --card-foreground: #e5e5e5;
    --popover: #262b38;
    --popover-foreground: #e5e5e5;
    --primary: #e05d38;
    --primary-foreground: #ffffff;
    --secondary: #2a303e;
    --secondary-foreground: #e5e5e5;
    --muted: #2a303e;
    --muted-foreground: #a3a3a3;
    --accent: #2a3656;
    --accent-foreground: #bfdbfe;
    --destructive: #ef4444;
    --destructive-foreground: #ffffff;
    --border: #3d4354;
    --input: #3d4354;
    --ring: #e05d38;
    --chart-1: #86a7c8;
    --chart-2: #e6a08f;
    --chart-3: #5a7ca6;
    --chart-4: #466494;
    --chart-5: #334c82;
    --sidebar: #2a303f;
    --sidebar-foreground: #e5e5e5;
    --sidebar-primary: #e05d38;
    --sidebar-primary-foreground: #ffffff;
    --sidebar-accent: #2a3656;
    --sidebar-accent-foreground: #bfdbfe;
    --sidebar-border: #3d4354;
    --sidebar-ring: #e05d38;
    --font-sans: Roboto Mono, monospace;
    --font-serif: Source Serif 4, serif;
    --font-mono: JetBrains Mono, monospace;
    --radius: 0.75rem;
    --shadow-2xs: 0px 1px 3px 0px hsl(0 0% 0% / 0.05);
    --shadow-xs: 0px 1px 3px 0px hsl(0 0% 0% / 0.05);
    --shadow-sm: 0px 1px 3px 0px hsl(0 0% 0% / 0.1), 0px 1px 2px -1px hsl(0 0% 0% / 0.1);
    --shadow: 0px 1px 3px 0px hsl(0 0% 0% / 0.1), 0px 1px 2px -1px hsl(0 0% 0% / 0.1);
    --shadow-md: 0px 1px 3px 0px hsl(0 0% 0% / 0.1), 0px 2px 4px -1px hsl(0 0% 0% / 0.1);
    --shadow-lg: 0px 1px 3px 0px hsl(0 0% 0% / 0.1), 0px 4px 6px -1px hsl(0 0% 0% / 0.1);
    --shadow-xl: 0px 1px 3px 0px hsl(0 0% 0% / 0.1), 0px 8px 10px -1px hsl(0 0% 0% / 0.1);
    --shadow-2xl: 0px 1px 3px 0px hsl(0 0% 0% / 0.25);
}

@theme inline {
    --color-background: var(--background);
    --color-foreground: var(--foreground);
    --color-card: var(--card);
    --color-card-foreground: var(--card-foreground);
    --color-popover: var(--popover);
    --color-popover-foreground: var(--popover-foreground);
    --color-primary: var(--primary);
    --color-primary-foreground: var(--primary-foreground);
    --color-secondary: var(--secondary);
    --color-secondary-foreground: var(--secondary-foreground);
    --color-muted: var(--muted);
    --color-muted-foreground: var(--muted-foreground);
    --color-accent: var(--accent);
    --color-accent-foreground: var(--accent-foreground);
    --color-destructive: var(--destructive);
    --color-destructive-foreground: var(--destructive-foreground);
    --color-border: var(--border);
    --color-input: var(--input);
    --color-ring: var(--ring);
    --color-chart-1: var(--chart-1);
    --color-chart-2: var(--chart-2);
    --color-chart-3: var(--chart-3);
    --color-chart-4: var(--chart-4);
    --color-chart-5: var(--chart-5);
    --color-sidebar: var(--sidebar);
    --color-sidebar-foreground: var(--sidebar-foreground);
    --color-sidebar-primary: var(--sidebar-primary);
    --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
    --color-sidebar-accent: var(--sidebar-accent);
    --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
    --color-sidebar-border: var(--sidebar-border);
    --color-sidebar-ring: var(--sidebar-ring);

    --font-sans: var(--font-sans);
    --font-mono: var(--font-mono);
    --font-serif: var(--font-serif);

    --radius-sm: calc(var(--radius) - 4px);
    --radius-md: calc(var(--radius) - 2px);
    --radius-lg: var(--radius);
    --radius-xl: calc(var(--radius) + 4px);

    --shadow-2xs: var(--shadow-2xs);
    --shadow-xs: var(--shadow-xs);
    --shadow-sm: var(--shadow-sm);
    --shadow: var(--shadow);
    --shadow-md: var(--shadow-md);
    --shadow-lg: var(--shadow-lg);
    --shadow-xl: var(--shadow-xl);
    --shadow-2xl: var(--shadow-2xl);
}
