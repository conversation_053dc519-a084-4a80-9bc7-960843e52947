<?php

namespace App\Http\Controllers;

use App\Models\Todo;
use App\Models\Category;
use Illuminate\Http\Request;
use Inertia\Inertia;

class DashboardController extends Controller
{
    public function index(Request $request)
    {
        $user = $request->user();

        // Get user's todos with categories
        $todos = $user->todos()
            ->with('category')
            ->orderBy('due_date', 'asc')
            ->orderBy('priority', 'desc')
            ->get();

        // Get user's categories
        $categories = $user->categories()->get();

        // Calculate statistics
        $stats = [
            'total' => $todos->count(),
            'pending' => $todos->where('status', 'pending')->count(),
            'completed' => $todos->where('status', 'completed')->count(),
            'overdue' => $todos->filter(function ($todo) {
                return $todo->isOverdue();
            })->count(),
            'high_priority' => $todos->where('priority', 'high')->where('status', 'pending')->count(),
        ];

        // Get recent todos (last 5 pending)
        $recentTodos = $todos->where('status', 'pending')->take(5);

        // Get upcoming todos (next 3 days)
        $upcomingTodos = $todos->where('status', 'pending')
            ->filter(function ($todo) {
                return $todo->due_date && $todo->due_date->between(now(), now()->addDays(3));
            })
            ->take(5);

        return Inertia::render('Dashboard', [
            'stats' => $stats,
            'recentTodos' => $recentTodos->values(),
            'upcomingTodos' => $upcomingTodos->values(),
            'categories' => $categories,
            'totalCategories' => $categories->count(),
        ]);
    }
}
