<?php

namespace App\Console\Commands;

use App\Models\User;
use App\Models\Category;
use App\Models\Todo;
use Illuminate\Console\Command;
use Carbon\Carbon;

class CreateSampleData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:create-sample-data {email}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create sample todos and categories for a user';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $email = $this->argument('email');
        $user = User::where('email', $email)->first();

        if (!$user) {
            $this->error("User with email {$email} not found!");
            return 1;
        }

        // Clear existing data for this user
        $user->todos()->delete();
        $user->categories()->delete();

        // Create categories
        $categories = [
            ['name' => 'Work', 'color' => '#e05d38', 'icon' => 'briefcase'],
            ['name' => 'Personal', 'color' => '#86a7c8', 'icon' => 'user'],
            ['name' => 'Health', 'color' => '#5a7ca6', 'icon' => 'heart'],
            ['name' => 'Learning', 'color' => '#466494', 'icon' => 'book'],
            ['name' => 'Shopping', 'color' => '#334c82', 'icon' => 'shopping-cart'],
            ['name' => 'Finance', 'color' => '#eea591', 'icon' => 'dollar-sign'],
        ];

        foreach ($categories as $categoryData) {
            Category::create([
                'user_id' => $user->id,
                'name' => $categoryData['name'],
                'color' => $categoryData['color'],
                'icon' => $categoryData['icon'],
            ]);
        }

        $this->info('Categories created successfully!');

        // Get created categories
        $userCategories = $user->categories;

        // Create sample todos
        $todos = [
            [
                'title' => 'Complete project proposal',
                'description' => 'Finish the quarterly project proposal for the new client',
                'priority' => 'high',
                'status' => 'pending',
                'due_date' => Carbon::now()->addDays(3)->format('Y-m-d'),
                'due_time' => '17:00:00',
                'category_name' => 'Work',
            ],
            [
                'title' => 'Buy groceries',
                'description' => 'Milk, bread, eggs, vegetables for the week',
                'priority' => 'medium',
                'status' => 'pending',
                'due_date' => Carbon::now()->addDays(1)->format('Y-m-d'),
                'due_time' => '18:00:00',
                'category_name' => 'Shopping',
            ],
            [
                'title' => 'Morning workout',
                'description' => '30 minutes cardio and strength training',
                'priority' => 'medium',
                'status' => 'completed',
                'due_date' => Carbon::now()->format('Y-m-d'),
                'due_time' => '07:00:00',
                'category_name' => 'Health',
            ],
            [
                'title' => 'Read Laravel documentation',
                'description' => 'Study new features in Laravel 12.x',
                'priority' => 'low',
                'status' => 'pending',
                'due_date' => Carbon::now()->addDays(7)->format('Y-m-d'),
                'due_time' => '20:00:00',
                'category_name' => 'Learning',
            ],
            [
                'title' => 'Pay utility bills',
                'description' => 'Electricity, water, and internet bills',
                'priority' => 'high',
                'status' => 'pending',
                'due_date' => Carbon::now()->addDays(2)->format('Y-m-d'),
                'due_time' => '12:00:00',
                'category_name' => 'Finance',
            ],
        ];

        foreach ($todos as $todoData) {
            $category = $userCategories->where('name', $todoData['category_name'])->first();

            Todo::create([
                'user_id' => $user->id,
                'title' => $todoData['title'],
                'description' => $todoData['description'],
                'priority' => $todoData['priority'],
                'status' => $todoData['status'],
                'due_date' => $todoData['due_date'],
                'due_time' => $todoData['due_time'],
                'category_id' => $category ? $category->id : null,
                'is_recurring' => false,
            ]);
        }

        $this->info('Sample todos created successfully!');
        $this->info("Created " . count($categories) . " categories and " . count($todos) . " todos for user: {$user->name}");

        return 0;
    }
}
