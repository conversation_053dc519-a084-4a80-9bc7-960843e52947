<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class TodoSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $users = DB::table('users')->get();

        foreach ($users as $user) {
            $categories = DB::table('categories')->where('user_id', $user->id)->get();

            if ($categories->isEmpty()) {
                continue;
            }

            $sampleTodos = [
                [
                    'title' => 'Complete project proposal',
                    'description' => 'Finish the quarterly project proposal for the new client',
                    'priority' => 'high',
                    'status' => 'pending',
                    'due_date' => Carbon::now()->addDays(3)->toDateString(),
                    'due_time' => '17:00:00',
                    'category_id' => $categories->where('name', 'Work')->first()?->id,
                ],
                [
                    'title' => 'Buy groceries',
                    'description' => 'Milk, bread, eggs, vegetables for the week',
                    'priority' => 'medium',
                    'status' => 'pending',
                    'due_date' => Carbon::now()->addDays(1)->toDateString(),
                    'due_time' => '18:00:00',
                    'category_id' => $categories->where('name', 'Shopping')->first()?->id,
                ],
                [
                    'title' => 'Morning workout',
                    'description' => '30 minutes cardio and strength training',
                    'priority' => 'medium',
                    'status' => 'completed',
                    'due_date' => Carbon::now()->toDateString(),
                    'due_time' => '07:00:00',
                    'category_id' => $categories->where('name', 'Health')->first()?->id,
                ],
                [
                    'title' => 'Read Laravel documentation',
                    'description' => 'Study new features in Laravel 12.x',
                    'priority' => 'low',
                    'status' => 'pending',
                    'due_date' => Carbon::now()->addDays(7)->toDateString(),
                    'due_time' => '20:00:00',
                    'category_id' => $categories->where('name', 'Learning')->first()?->id,
                ],
                [
                    'title' => 'Pay utility bills',
                    'description' => 'Electricity, water, and internet bills',
                    'priority' => 'high',
                    'status' => 'pending',
                    'due_date' => Carbon::now()->addDays(2)->toDateString(),
                    'due_time' => '12:00:00',
                    'category_id' => $categories->where('name', 'Finance')->first()?->id,
                ],
            ];

            foreach ($sampleTodos as $todo) {
                DB::table('todos')->insert([
                    'user_id' => $user->id,
                    'title' => $todo['title'],
                    'description' => $todo['description'],
                    'priority' => $todo['priority'],
                    'status' => $todo['status'],
                    'due_date' => $todo['due_date'],
                    'due_time' => $todo['due_time'],
                    'category_id' => $todo['category_id'],
                    'is_recurring' => false,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }
        }
    }
}
